<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Webinar;
use App\Http\Controllers\WebinarAnalyticsController;

class TestAnalytics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:analytics {webinar_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test webinar analytics functionality';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $webinarId = $this->argument('webinar_id') ?: 1;
        $webinar = Webinar::find($webinarId);

        if (!$webinar) {
            $this->error("Webinar with ID {$webinarId} not found!");
            return 1;
        }

        $this->info("Testing analytics for webinar: {$webinar->title}");

        // Test basic metrics
        $totalParticipants = $webinar->participants()->count();
        $uniqueParticipants = $webinar->participants()->distinct('email')->count('email');
        $totalOrders = $webinar->orders()->count();
        $totalRevenue = $webinar->orders()->sum('price');

        $this->table(['Metric', 'Value'], [
            ['Total Participants', $totalParticipants],
            ['Unique Participants', $uniqueParticipants],
            ['Total Orders', $totalOrders],
            ['Total Revenue', number_format($totalRevenue) . ' VND'],
            ['Conversion Rate', $uniqueParticipants > 0 ? round(($totalOrders / $uniqueParticipants) * 100, 2) . '%' : '0%'],
        ]);

        // Test controller methods
        try {
            $controller = new WebinarAnalyticsController();
            $reflection = new \ReflectionClass($controller);

            // Test getOrderStats
            $method = $reflection->getMethod('getOrderStats');
            $method->setAccessible(true);
            $orderStats = $method->invoke($controller, $webinar);

            $this->info("\n📊 Order Stats:");
            $this->line("- Total Orders: {$orderStats['total_orders']}");
            $this->line("- Total Revenue: " . number_format($orderStats['total_revenue']) . " VND");
            $this->line("- Conversion Rate: {$orderStats['conversion_rate']}%");
            $this->line("- Revenue per Participant: " . number_format($orderStats['revenue_per_participant']) . " VND");

            // Test getQuestionStats
            $method = $reflection->getMethod('getQuestionStats');
            $method->setAccessible(true);
            $questionStats = $method->invoke($controller, $webinar);

            $this->info("\n❓ Question Stats:");
            $this->line("- Total Questions: {$questionStats['total_questions']}");
            $this->line("- Total Responses: {$questionStats['total_responses']}");
            $this->line("- Response Rate: {$questionStats['response_rate']}%");

            // Test getEngagementStats
            $method = $reflection->getMethod('getEngagementStats');
            $method->setAccessible(true);
            $engagementStats = $method->invoke($controller, $webinar);

            $this->info("\n💝 Engagement Stats:");
            $this->line("- Engagement Rate: {$engagementStats['engagement_rate']}%");
            $this->line("- Bounce Rate: {$engagementStats['bounce_rate']}%");
            $this->line("- Avg Session Duration: {$engagementStats['avg_session_duration']} minutes");
            $this->line("- Peak Concurrent: {$engagementStats['peak_concurrent']}");

            $this->info("\n✅ All analytics methods working correctly!");

        } catch (\Exception $e) {
            $this->error("❌ Error testing analytics: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
