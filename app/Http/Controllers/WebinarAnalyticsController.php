<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Webinar;
use App\Models\WebinarParticipant;
use App\Models\WebinarComment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class WebinarAnalyticsController extends Controller
{
    /**
     * Tạo controller instance mới
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Hiển thị trang phân tích webinar
     *
     * @param  \App\Models\Webinar  $webinar
     * @return \Illuminate\Http\Response
     */
    public function show(Webinar $webinar)
    {
        // Kiểm tra quyền truy cập
        if (!Auth::user()->hasRole('administrator') && Auth::id() !== $webinar->user_id) {
            abort(403, 'Bạn không có quyền truy cập phân tích của webinar này.');
        }

        // Thống kê tham gia
        $totalParticipants = $webinar->participants()->count();
        $uniqueParticipants = $webinar->participants()->distinct('email')->count('email');

        // Kiểm tra xem cột view_duration đã tồn tại trong bảng chưa
        $hasViewDuration = Schema::hasColumn('webinar_participants', 'view_duration');
        $hasDeviceType = Schema::hasColumn('webinar_participants', 'device_type');

        // Tính thời gian xem trung bình (nếu có cột view_duration)
        $avgViewDuration = 0;
        if ($hasViewDuration) {
            $avgViewDuration = $webinar->participants()
                ->whereNotNull('view_duration')
                ->avg('view_duration') ?? 0;
        }

        // Phân tích theo thiết bị (nếu có cột device_type)
        $deviceStats = [];
        if ($hasDeviceType) {
            $deviceStats = $webinar->participants()
                ->select('device_type', DB::raw('count(*) as count'))
                ->whereNotNull('device_type')
                ->groupBy('device_type')
                ->get()
                ->pluck('count', 'device_type')
                ->toArray();
        } else {
            // Mô phỏng dữ liệu khi chưa có cột device_type
            $deviceStats = [
                'desktop' => $totalParticipants > 0 ? intval($totalParticipants * 0.6) : 60,
                'mobile' => $totalParticipants > 0 ? intval($totalParticipants * 0.3) : 30,
                'tablet' => $totalParticipants > 0 ? intval($totalParticipants * 0.1) : 10,
            ];
        }

        // Thời gian tham gia phổ biến
        $joinTimeDistribution = $webinar->participants()
            ->select(DB::raw('HOUR(joined_at) as hour'), DB::raw('COUNT(*) as count'))
            ->groupBy('hour')
            ->orderBy('hour')
            ->get();

        // Thống kê bình luận
        $totalComments = $webinar->comments()->count();
        $commentTimes = $webinar->comments()
            ->select(DB::raw('COUNT(*) as count'), 'video_timestamp')
            ->groupBy('video_timestamp')
            ->orderBy('video_timestamp')
            ->get();

        // === METRICS MỚI: CONVERSION & BUSINESS ===

        // Thống kê đơn hàng và conversion
        $orderStats = $this->getOrderStats($webinar);

        // Thống kê câu hỏi và tương tác
        $questionStats = $this->getQuestionStats($webinar);

        // Engagement metrics nâng cao
        $engagementStats = $this->getEngagementStats($webinar);

        // Behavioral analytics
        $behaviorStats = $this->getBehaviorStats($webinar);

        // Lấy dữ liệu quảng cáo
        $adClicks = [];
        if ($webinar->advertisement_slots) {
            // Lấy danh sách quảng cáo thông qua accessor
            $advertisements = $webinar->getAdvertisementsAttribute();

            foreach ($webinar->advertisement_slots as $index => $slot) {
                $adName = 'Quảng cáo ' . ($index + 1);

                // Kiểm tra an toàn trước khi truy cập dữ liệu quảng cáo
                if (isset($slot['advertisement_id']) && !empty($slot['advertisement_id'])) {
                    $advertisement = $advertisements->where('id', $slot['advertisement_id'])->first();
                    if ($advertisement && !empty($advertisement->name)) {
                        $adName = $advertisement->name;
                    }
                }

                $adClicks[] = [
                    'time' => $slot['time'] ?? "00:00:00",
                    'clicks' => mt_rand(5, 50), // Mô phỏng số liệu
                    'views' => mt_rand(50, 200), // Mô phỏng số liệu
                    'name' => $adName
                ];
            }
        }

        // Dữ liệu tham gia theo thời gian
        $registrationTrend = $this->getRegistrationTrend($webinar);

        // Mô phỏng dữ liệu bỏ học theo thời gian (giả sử đã có dữ liệu thực tế)
        $retentionData = $this->generateRetentionData();

        // Thống kê dữ liệu UTM
        $utmSourceStats = $this->getUtmSourceStats($webinar);
        $utmMediumStats = $this->getUtmMediumStats($webinar);
        $utmCampaignStats = $this->getUtmCampaignStats($webinar);

        return view('webinars.analytics', compact(
            'webinar',
            'totalParticipants',
            'uniqueParticipants',
            'avgViewDuration',
            'deviceStats',
            'joinTimeDistribution',
            'totalComments',
            'commentTimes',
            'adClicks',
            'registrationTrend',
            'retentionData',
            'utmSourceStats',
            'utmMediumStats',
            'utmCampaignStats',
            // Metrics mới
            'orderStats',
            'questionStats',
            'engagementStats',
            'behaviorStats'
        ));
    }

    /**
     * Lấy dữ liệu xu hướng đăng ký tham gia
     */
    private function getRegistrationTrend(Webinar $webinar)
    {
        $createdAt = Carbon::parse($webinar->created_at);
        $now = Carbon::now();
        $diffInDays = $createdAt->diffInDays($now);

        // Nếu webinar được tạo dưới 7 ngày, lấy dữ liệu theo giờ
        if ($diffInDays < 7) {
            $participants = $webinar->participants()
                ->select(DB::raw('DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00") as date'), DB::raw('COUNT(*) as count'))
                ->where('created_at', '>=', $createdAt)
                ->groupBy('date')
                ->orderBy('date')
                ->get();

            return [
                'labels' => $participants->pluck('date')->map(function($date) {
                    return Carbon::parse($date)->format('H:00 d/m');
                }),
                'data' => $participants->pluck('count'),
            ];
        }

        // Nếu trên 7 ngày, lấy dữ liệu theo ngày
        $participants = $webinar->participants()
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('COUNT(*) as count'))
            ->where('created_at', '>=', $createdAt)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'labels' => $participants->pluck('date')->map(function($date) {
                return Carbon::parse($date)->format('d/m/Y');
            }),
            'data' => $participants->pluck('count'),
        ];
    }

    /**
     * Tạo dữ liệu giữ chân người xem (mô phỏng)
     */
    private function generateRetentionData()
    {
        // Mô phỏng dữ liệu: 5 phút đầu, 15 phút, 30 phút, 45 phút, 60 phút
        $timePoints = ['5 phút', '15 phút', '30 phút', '45 phút', '60 phút'];

        // Bắt đầu với 100%, giảm dần
        $percentages = [100, mt_rand(75, 95), mt_rand(50, 75), mt_rand(30, 50), mt_rand(20, 30)];

        return [
            'labels' => $timePoints,
            'data' => $percentages
        ];
    }

    /**
     * Lấy dữ liệu cho API
     */
    public function getAnalyticsData(Webinar $webinar)
    {
        // Kiểm tra quyền truy cập
        if (!Auth::user()->hasRole('administrator') && Auth::id() !== $webinar->user_id) {
            return response()->json(['error' => 'Không có quyền truy cập'], 403);
        }

        // Kiểm tra xem cột view_duration đã tồn tại trong bảng chưa
        $hasViewDuration = Schema::hasColumn('webinar_participants', 'view_duration');

        // Thống kê tham gia
        $participantStats = [
            'total' => $webinar->participants()->count(),
            'unique' => $webinar->participants()->distinct('email')->count('email'),
            'avgDuration' => $hasViewDuration ?
                round($webinar->participants()->whereNotNull('view_duration')->avg('view_duration') ?? 0) : 0,
        ];

        return response()->json([
            'participant_stats' => $participantStats,
            'registration_trend' => $this->getRegistrationTrend($webinar),
            'retention_data' => $this->generateRetentionData(),
            'utm_source_stats' => $this->getUtmSourceStats($webinar),
            'utm_medium_stats' => $this->getUtmMediumStats($webinar),
            'utm_campaign_stats' => $this->getUtmCampaignStats($webinar),
        ]);
    }

    /**
     * Lấy thống kê UTM Source
     */
    private function getUtmSourceStats(Webinar $webinar)
    {
        $stats = $webinar->participants()
            ->select('utm_source', DB::raw('COUNT(*) as count'))
            ->whereNotNull('utm_source')
            ->groupBy('utm_source')
            ->orderBy('count', 'desc')
            ->get();

        // Nếu không có dữ liệu thì thêm "-" làm giá trị mặc định
        if ($stats->isEmpty()) {
            $stats->push(['utm_source' => 'Không xác định', 'count' => $webinar->participants()->whereNull('utm_source')->count()]);
        }

        return [
            'labels' => $stats->pluck('utm_source'),
            'data' => $stats->pluck('count'),
        ];
    }

    /**
     * Lấy thống kê UTM Medium
     */
    private function getUtmMediumStats(Webinar $webinar)
    {
        $stats = $webinar->participants()
            ->select('utm_medium', DB::raw('COUNT(*) as count'))
            ->whereNotNull('utm_medium')
            ->groupBy('utm_medium')
            ->orderBy('count', 'desc')
            ->get();

        // Nếu không có dữ liệu thì thêm "-" làm giá trị mặc định
        if ($stats->isEmpty()) {
            $stats->push(['utm_medium' => 'Không xác định', 'count' => $webinar->participants()->whereNull('utm_medium')->count()]);
        }

        return [
            'labels' => $stats->pluck('utm_medium'),
            'data' => $stats->pluck('count'),
        ];
    }

    /**
     * Lấy thống kê UTM Campaign
     */
    private function getUtmCampaignStats(Webinar $webinar)
    {
        $stats = $webinar->participants()
            ->select('utm_campaign', DB::raw('COUNT(*) as count'))
            ->whereNotNull('utm_campaign')
            ->groupBy('utm_campaign')
            ->orderBy('count', 'desc')
            ->get();

        // Nếu không có dữ liệu thì thêm "-" làm giá trị mặc định
        if ($stats->isEmpty()) {
            $stats->push(['utm_campaign' => 'Không xác định', 'count' => $webinar->participants()->whereNull('utm_campaign')->count()]);
        }

        return [
            'labels' => $stats->pluck('utm_campaign'),
            'data' => $stats->pluck('count'),
        ];
    }

    /**
     * Lấy thống kê đơn hàng và conversion
     */
    private function getOrderStats(Webinar $webinar)
    {
        $orders = $webinar->orders();

        $totalOrders = $orders->count();
        $totalRevenue = $orders->sum('price');
        $paidOrders = $orders->where('payment_status', 'paid')->count();
        $uniqueParticipants = $webinar->participants()->distinct('email')->count('email');
        $conversionRate = $uniqueParticipants > 0 ?
            round(($totalOrders / $uniqueParticipants) * 100, 2) : 0;
        $revenuePerParticipant = $uniqueParticipants > 0 ?
            round($totalRevenue / $uniqueParticipants, 0) : 0;

        // Phân bố trạng thái đơn hàng
        $orderStatusDistribution = $orders
            ->select('payment_status', DB::raw('COUNT(*) as count'))
            ->groupBy('payment_status')
            ->get()
            ->pluck('count', 'payment_status')
            ->toArray();

        // Doanh thu theo thời gian
        $revenueByTime = $orders
            ->select(DB::raw('DATE(created_at) as date'), DB::raw('SUM(price) as revenue'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total_orders' => $totalOrders,
            'total_revenue' => $totalRevenue,
            'paid_orders' => $paidOrders,
            'conversion_rate' => $conversionRate,
            'revenue_per_participant' => $revenuePerParticipant,
            'order_status_distribution' => $orderStatusDistribution,
            'revenue_trend' => [
                'labels' => $revenueByTime->pluck('date'),
                'data' => $revenueByTime->pluck('revenue'),
            ]
        ];
    }

    /**
     * Lấy thống kê câu hỏi và khảo sát
     */
    private function getQuestionStats(Webinar $webinar)
    {
        // Lấy tất cả questions của webinar
        $questions = \App\Models\Question::where('webinar_id', $webinar->id);
        $totalQuestions = $questions->count();
        $activeQuestions = $questions->where('status', true)->count();

        // Lấy tất cả responses
        $responses = \App\Models\QuestionResponse::where('webinar_id', $webinar->id);
        $totalResponses = $responses->count();
        $uniqueRespondents = $responses->distinct('session_id')->count();
        $uniqueParticipants = $webinar->participants()->distinct('email')->count('email');

        // Tính response rate
        $responseRate = $uniqueParticipants > 0 && $activeQuestions > 0 ?
            round(($uniqueRespondents / $uniqueParticipants) * 100, 2) : 0;

        // Phân bố theo loại câu hỏi
        $questionTypeDistribution = $questions
            ->select('type', DB::raw('COUNT(*) as count'))
            ->groupBy('type')
            ->get()
            ->pluck('count', 'type')
            ->toArray();

        // Responses theo thời gian
        $responsesByTime = $responses
            ->select(DB::raw('DATE(responded_at) as date'), DB::raw('COUNT(*) as count'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'total_questions' => $totalQuestions,
            'active_questions' => $activeQuestions,
            'total_responses' => $totalResponses,
            'unique_respondents' => $uniqueRespondents,
            'response_rate' => $responseRate,
            'question_type_distribution' => $questionTypeDistribution,
            'responses_trend' => [
                'labels' => $responsesByTime->pluck('date'),
                'data' => $responsesByTime->pluck('count'),
            ]
        ];
    }

    /**
     * Lấy thống kê engagement nâng cao
     */
    private function getEngagementStats(Webinar $webinar)
    {
        $participants = $webinar->participants();
        $comments = $webinar->comments();
        $responses = \App\Models\QuestionResponse::where('webinar_id', $webinar->id);

        // Tính engagement rate
        $totalInteractions = $comments->count() + $responses->count();
        $uniqueParticipants = $webinar->participants()->distinct('email')->count('email');
        $totalParticipants = $webinar->participants()->count();
        $engagementRate = $uniqueParticipants > 0 ?
            round(($totalInteractions / $uniqueParticipants) * 100, 2) : 0;

        // Tính bounce rate (người rời đi trong 5 phút đầu)
        $hasViewDuration = Schema::hasColumn('webinar_participants', 'view_duration');
        $bounceRate = 0;
        if ($hasViewDuration) {
            $shortSessions = $participants->where('view_duration', '<', 300)->count(); // < 5 phút
            $bounceRate = $totalParticipants > 0 ?
                round(($shortSessions / $totalParticipants) * 100, 2) : 0;
        }

        // Average session duration (phút)
        $avgSessionDuration = 0;
        if ($hasViewDuration) {
            $avgSessionDuration = round(($participants->avg('view_duration') ?? 0) / 60, 1);
        }

        // Peak concurrent users (mô phỏng dựa trên join time)
        $peakConcurrent = $this->calculatePeakConcurrent($webinar);

        // Interaction timeline
        $interactionTimeline = $this->getInteractionTimeline($webinar);

        return [
            'engagement_rate' => $engagementRate,
            'bounce_rate' => $bounceRate,
            'avg_session_duration' => $avgSessionDuration,
            'peak_concurrent' => $peakConcurrent,
            'total_interactions' => $totalInteractions,
            'interaction_timeline' => $interactionTimeline
        ];
    }

    /**
     * Lấy thống kê hành vi người dùng
     */
    private function getBehaviorStats(Webinar $webinar)
    {
        // Phân tích repeat visitors
        $repeatVisitors = $webinar->participants()
            ->where('join_count', '>', 1)
            ->count();
        $uniqueParticipants = $webinar->participants()->distinct('email')->count('email');

        $repeatRate = $uniqueParticipants > 0 ?
            round(($repeatVisitors / $uniqueParticipants) * 100, 2) : 0;

        // Phân bố thời gian tham gia
        $joinHourDistribution = $webinar->participants()
            ->select(DB::raw('HOUR(joined_at) as hour'), DB::raw('COUNT(*) as count'))
            ->whereNotNull('joined_at')
            ->groupBy('hour')
            ->orderBy('hour')
            ->get()
            ->pluck('count', 'hour')
            ->toArray();

        // Geographic distribution (mô phỏng từ IP)
        $geoDistribution = $this->getGeographicDistribution($webinar);

        // User journey analysis
        $userJourney = $this->getUserJourneyStats($webinar);

        return [
            'repeat_visitors' => $repeatVisitors,
            'repeat_rate' => $repeatRate,
            'join_hour_distribution' => $joinHourDistribution,
            'geographic_distribution' => $geoDistribution,
            'user_journey' => $userJourney
        ];
    }

    /**
     * Tính peak concurrent users
     */
    private function calculatePeakConcurrent(Webinar $webinar)
    {
        // Mô phỏng peak concurrent dựa trên pattern thông thường
        // Trong thực tế cần tracking real-time
        $totalParticipants = $webinar->participants()->count();

        if ($totalParticipants == 0) return 0;

        // Giả định peak concurrent là 60-80% total participants
        return round($totalParticipants * 0.7);
    }

    /**
     * Lấy timeline tương tác
     */
    private function getInteractionTimeline(Webinar $webinar)
    {
        $timeline = [];

        // Combine comments và responses theo thời gian
        $comments = $webinar->comments()
            ->select('created_at', DB::raw("'comment' as type"))
            ->get();

        $responses = \App\Models\QuestionResponse::where('webinar_id', $webinar->id)
            ->select('responded_at as created_at', DB::raw("'response' as type"))
            ->get();

        $allInteractions = $comments->concat($responses)
            ->sortBy('created_at')
            ->groupBy(function($item) {
                return Carbon::parse($item->created_at)->format('H:i');
            });

        foreach ($allInteractions as $time => $interactions) {
            $timeline[] = [
                'time' => $time,
                'count' => $interactions->count(),
                'comments' => $interactions->where('type', 'comment')->count(),
                'responses' => $interactions->where('type', 'response')->count()
            ];
        }

        return $timeline;
    }

    /**
     * Lấy phân bố địa lý (mô phỏng)
     */
    private function getGeographicDistribution(Webinar $webinar)
    {
        // Trong thực tế cần sử dụng IP geolocation service
        // Hiện tại mô phỏng dữ liệu
        $totalParticipants = $webinar->participants()->count();

        if ($totalParticipants == 0) {
            return [
                'labels' => ['Hà Nội', 'TP.HCM', 'Đà Nẵng', 'Khác'],
                'data' => [0, 0, 0, 0]
            ];
        }

        return [
            'labels' => ['Hà Nội', 'TP.HCM', 'Đà Nẵng', 'Khác'],
            'data' => [
                round($totalParticipants * 0.4), // 40% Hà Nội
                round($totalParticipants * 0.35), // 35% TP.HCM
                round($totalParticipants * 0.15), // 15% Đà Nẵng
                round($totalParticipants * 0.1)   // 10% Khác
            ]
        ];
    }

    /**
     * Phân tích user journey
     */
    private function getUserJourneyStats(Webinar $webinar)
    {
        $participants = $webinar->participants();

        // Participants có email (potential leads)
        $withEmail = $participants->whereNotNull('email')->count();
        $withPhone = $participants->whereNotNull('phone')->count();
        $withBoth = $participants->whereNotNull('email')
            ->whereNotNull('phone')->count();

        // Conversion funnel
        $totalVisitors = $participants->count();
        $engaged = $webinar->comments()->distinct('participant_id')->count();
        $orders = $webinar->orders()->count();

        return [
            'lead_quality' => [
                'with_email' => $withEmail,
                'with_phone' => $withPhone,
                'with_both' => $withBoth,
                'email_rate' => $totalVisitors > 0 ? round(($withEmail / $totalVisitors) * 100, 1) : 0,
                'phone_rate' => $totalVisitors > 0 ? round(($withPhone / $totalVisitors) * 100, 1) : 0
            ],
            'conversion_funnel' => [
                'visitors' => $totalVisitors,
                'engaged' => $engaged,
                'orders' => $orders,
                'engagement_rate' => $totalVisitors > 0 ? round(($engaged / $totalVisitors) * 100, 1) : 0,
                'order_rate' => $totalVisitors > 0 ? round(($orders / $totalVisitors) * 100, 1) : 0
            ]
        ];
    }
}
