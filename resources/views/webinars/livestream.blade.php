@extends('layouts.app')

@php
use App\Models\Module;
@endphp

@section('content')
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="main-title m-0">
            <i class="fas fa-info-circle"></i>QUẢN LÝ LIVESTREAM WEBINAR
        </h1>
        <div class="d-flex flex-wrap gap-2">
            @if(Module::isEnabled('livestream_education'))
                <a href="{{ route('webinars.livestream.class', $webinar) }}"
                   class="btn btn-success d-flex flex-column align-items-center p-2">
                    <i class="fas fa-rss mb-1 fs-5"></i>
                    <span class="btn-text">LiveStream Lớp học</span>
                </a>
            @endif
            @if(Module::isEnabled('livestream_marketing'))
                <a href="{{ route('webinars.livestream', $webinar) }}"
                   class="btn btn-success d-flex flex-column align-items-center p-2">
                    <i class="fas fa-rss mb-1 fs-5"></i>
                    <span class="btn-text">LiveStream</span>
                </a>
            @endif
            @can('update', $webinar)
                <a href="{{ route('webinars.edit', $webinar) }}"
                   class="btn btn-primary d-flex flex-column align-items-center p-2">
                    <i class="fas fa-edit mb-1 fs-5"></i>
                    <span class="btn-text">Chỉnh sửa</span>
                </a>
                <a href="{{ route('webinars.upload', $webinar) }}"
                   class="btn btn-success d-flex flex-column align-items-center p-2">
                    <i class="fas fa-upload mb-1 fs-5"></i>
                    <span class="btn-text">Tải video</span>
                </a>

                <a href="{{ route('webinars.analytics', $webinar) }}"
                   class="btn btn-warning d-flex flex-column align-items-center p-2">
                    <i class="fas fa-chart-line mb-1 fs-5"></i>
                    <span class="btn-text">Phân tích</span>
                    @if(!Schema::hasColumn('webinar_participants', 'view_duration'))
                        <span class="badge bg-info position-absolute top-0 end-0" data-bs-toggle="tooltip"
                              data-bs-placement="top"
                              title="Tính năng đang được phát triển">Beta</span>
                    @endif
                </a>
            @endcan

            <a href="{{ route('participants.index', $webinar) }}"
               class="btn btn-info d-flex flex-column align-items-center p-2">
                <i class="fas fa-users mb-1 fs-5"></i>
                <span class="btn-text">Người xem</span>
                <span
                    class="badge bg-light text-dark position-absolute top-0 end-0">{{ $webinar->participants->count() }}</span>
            </a>

            <a href="{{ route('comments.by_schedule', $webinar) }}"
               class="btn btn-secondary d-flex flex-column align-items-center p-2">
                <i class="fas fa-comments mb-1 fs-5"></i>
                <span class="btn-text">Bình luận</span>
            </a>

            <a href="{{ route('webinars.index') }}" class="btn btn-light d-flex flex-column align-items-center p-2">
                <i class="fas fa-arrow-left mb-1 fs-5"></i>
                <span class="btn-text">Quay lại</span>
            </a>
        </div>
    </div>

    <!-- Real-time Statistics Dashboard -->
    <div class="analytics-dashboard mb-4">
        <div class="dashboard-header">
            <div class="revenue-section">
                <div class="revenue-label">Doanh thu (VNĐ)</div>
                <div class="revenue-amount" id="total-revenue">{{$total_amount_order}}</div>
            </div>

            <div class="live-info-section">
                @if(isset($webinar->livestreams["is_live"]) && $webinar->livestreams["is_live"]==1)
                    <div class="live-badge active">
                        <span class="live-dot"></span>
                        <span class="live-text">ĐANG LIVE</span>
                    </div>
                    <div class="live-time-info">
                        <div class="live-start-time">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <span id="current-date">{{ date('d/m/Y') }}</span>
                            <span id="current-time">{{ date('H:i:s') }}</span>
                        </div>
                        <div class="live-duration-display">
                            <i class="fas fa-clock me-1"></i>
                            <span class="duration-label">Thời gian live:</span>
                            <span class="live-timer" id="live-timer">00:00:00</span>
                        </div>
                    </div>
                @else
                    <div class="live-badge inactive">
                        <span class="offline-dot"></span>
                        <span class="offline-text">OFFLINE</span>
                    </div>
                    <div class="offline-info">
                        <div class="current-time-display">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <span id="current-date-offline">{{ date('d/m/Y') }}</span>
                            <span id="current-time-offline">{{ date('H:i:s') }}</span>
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <div class="metrics-grid">
            <div class="metric-item">
                <div class="metric-icon">👥</div>
                <div class="metric-content">
                    <div class="metric-label">Người đang xem</div>
                    <div class="metric-value" id="live-viewers">{{ $total_view_online }}</div>
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-icon">📦</div>
                <div class="metric-content">
                    <div class="metric-label">Tổng đơn hàng đã thanh toán</div>
                    <div class="metric-value" id="total-orders">{{ $total_order_paid }}</div>
                </div>
            </div>
            <div class="metric-item">
                <div class="metric-icon">📦</div>
                <div class="metric-content">
                    <div class="metric-label">Tổng đơn hàng chờ thanh toán</div>
                    <div class="metric-value" id="total-orders-pending">{{ $total_order_pending }}</div>
                </div>
            </div>

            <div class="metric-item">
                <div class="metric-icon">👤</div>
                <div class="metric-content">
                    <div class="metric-label">Tổng người xem</div>
                    <div class="metric-value" id="total-viewers">{{ $total_view }}</div>
                </div>
            </div>
        </div>

    @if(isset($webinar->livestreams["is_live"]) && $webinar->livestreams["is_live"]==1)
        <!-- Simple Live Warning -->
            <div class="alert alert-warning d-flex align-items-center mt-3" role="alert">
                <div>
                    <strong>⚠️ LƯU Ý QUAN TRỌNG:</strong>
                    KHÔNG TẮT màn hình này khi đang livestream! Hệ thống sẽ tự động ngắt kết nối sau 5 phút không hoạt
                    động.
                </div>
            </div>
        @endif
    </div>

    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h2 class="text-dark mb-3 fw-bold">
                        <i class="fas fa-video text-primary me-2"></i> Tên Webinar: {{ $webinar->title }}
                    </h2>
                    <p class="text-muted mb-4 d-flex align-items-center">
                        <i class="fas fa-user-tie text-primary me-2"></i> Người Thuyết Trình: <span
                            class="fw-semibold ms-1">{{ $webinar->speaker }}</span>
                    </p>
                    <div class="card mb-4">
                        <div class="card-header d-flex align-items-center">
                            <i class="fas fa-link text-primary me-2"></i>
                            <h5 class="mb-0">Cấu hình Livestream</h5>
                        </div>
                        <div class="card-body">
                            <form action="{{route("webinars.livestream.post",$webinar)}}" method="POST">
                                @csrf
                                <div class="form-group">
                                    <label class="form-label">Link live</label>
                                    <div class="input-group">
                                        <select name="type_live" class="form-select" style="    flex: 0.2 1 auto;">
                                            <option
                                                {{old("type_live",$webinar->livestreams["type"]??null)=="youtube"?"selected":''}} value="youtube">
                                                Youtube
                                            </option>
                                            <option
                                                {{old("type_live",$webinar->livestreams["type"]??null)=="facebook"?"selected":''}} value="facebook">
                                                Facebook
                                            </option>
                                            <option
                                                {{old("type_live",$webinar->livestreams["type"]??null)=="m3u8"?"selected":''}} value="m3u8">
                                                Link m3u8
                                            </option>
                                            <option
                                                {{old("type_live",$webinar->livestreams["type"]??null)=="jitsi"?"selected":''}} value="jitsi">
                                                Jitsi
                                            </option>
                                        </select>
                                        <input type="text" class="form-control" name="link_live"
                                               value="{{old("link_live",$webinar->livestreams["link"]??"")}}"
                                               placeholder="Nhập link live. VD: https://www.facebook.com/IamRubyNguyen/videos/2474717342891862/">
                                    </div>
                                    @error("link_live")
                                    <span class="text-danger">{{$message}}</span>
                                    @enderror
                                    @error("type_live")
                                    <span class="text-danger">{{$message}}</span>
                                    @enderror
                                </div>
                                <div class="form-group mt-3">
                                    <label for="" class="form-label">Chọn quảng cáo hiển thị</label>
                                    <div class="input-group">
                                        <select name="advertisement_id" id="advertisement_id" class="form-select">
                                            <option value="">Chọn quảng cáo</option>
                                            @foreach($advertisements as $id=>$value)
                                                <option
                                                    {{old("advertisement_id",$webinar->livestreams["advertisement_id"]??null)==$id?"selected":''}} value="{{$id}}">{{$value}}</option>
                                            @endforeach
                                        </select>
                                        @if(isset($webinar->livestreams["is_enabled"]) && $webinar->livestreams["is_enabled"]==1)
                                            <button
                                                {{!isset($webinar->livestreams["is_live"]) || $webinar->livestreams["is_live"]==0?"disabled":""}} class="btn btn-outline-primary toggle-ads"
                                                type="button"
                                                data-url="{{route("webinars.livestream.push.advertisement",$webinar)}}">
                                                <i class="fa-solid fa-eye-low-vision"></i> Tắt
                                            </button>
                                        @else
                                            <button
                                                {{!isset($webinar->livestreams["is_live"]) || $webinar->livestreams["is_live"]==0?"disabled":""}} class="btn btn-outline-primary toggle-ads enable"
                                                type="button"
                                                data-url="{{route("webinars.livestream.push.advertisement",$webinar)}}">
                                                <i class="fas fa-eye me-1"></i> Hiển thị
                                            </button>

                                        @endif
                                    </div>

                                    @error("advertisement_id")
                                    <span class="text-danger">{{$message}}</span>
                                    @enderror
                                </div>

                                <div class="form-group mt-3">
                                    @if(isset($webinar->livestreams["is_live"]) && $webinar->livestreams["is_live"]==1)
                                        <button class="btn btn-outline-primary start-live" name="is_live" value="0"
                                                type="submit">
                                            <i class="fas fa-rss me-1"></i> Kết thúc live
                                        </button>
                                    @else
                                        <button class="btn btn-outline-primary start-live" name="is_live" value="1"
                                                type="submit">
                                            <i class="fas fa-rss me-1"></i>Bắt đầu livestream
                                        </button>
                                    @endif
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>
        $(document).ready(function () {

            $(".toggle-ads").click(function () {
                const $button = $(this); // Lưu lại button được click
                const isEnable = $button.hasClass("enable");
                const originalHtml = isEnable
                    ? `<i class="fas fa-eye me-1"></i> Hiển thị`
                    : `<i class="fa-solid fa-eye-low-vision"></i> Tắt`;

                // Hiển thị spinner
                $button.html(`<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...`);

                const url = $button.data("url");
                const advertisement_id = $('select[name="advertisement_id"]').val();

                const formData = {
                    _token: "{{csrf_token()}}",
                    advertisement_id: advertisement_id,
                    status: isEnable ? 1 : 0,
                };

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: formData,
                    success: function (response) {
                        if (response.error) {
                            Swal.fire({
                                title: response.mess,
                                icon: 'error',
                                confirmButtonText: 'OK'
                            });
                            $button.html(originalHtml);
                            if (isEnable) {
                                $button.addClass("enable");
                            } else {
                                $button.removeClass("enable");
                            }
                        } else {
                            if (response.flag) {
                                $button.removeClass("enable");
                                $button.html(`<i class="fa-solid fa-eye-low-vision"></i> Tắt`);
                            } else {
                                $button.addClass("enable");
                                $button.html(`<i class="fas fa-eye me-1"></i> Hiển thị`);
                            }
                            Swal.fire({
                                title: response.mess,
                                icon: 'success',
                                confirmButtonText: 'OK'
                            });
                        }
                    },
                    error: function () {
                        // Nếu AJAX lỗi, quay lại trạng thái ban đầu
                        Swal.fire({
                            title: "Có lỗi xảy ra khi xử lý yêu cầu.",
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                        $button.html(originalHtml);
                        if (isEnable) {
                            $button.addClass("enable");
                        } else {
                            $button.removeClass("enable");
                        }
                    }
                });
            });

            $(".start-live").click(function () {
                if ($(this).hasClass("is_live")) {
                    $(this).removeClass("is_live")
                    $(this).html(`<i class="fas fa-rss me-1"></i>Bắt đầu livestream`)
                } else {
                    $(this).addClass("is_live")
                    $(this).html(`<i class="fas fa-rss me-1"></i> Kết thúc live`)
                }
            });

            // Statistics Dashboard Functionality
            let liveStartTime = null;
            let liveTimer = null;
            let realtimeTimer = null;

            // Initialize dashboard
            $('.analytics-dashboard').addClass('loaded');

            // Start realtime clock
            function startRealtimeClock() {
                realtimeTimer = setInterval(function () {
                    const now = new Date();
                    const dateStr = now.toLocaleDateString('vi-VN', {
                        day: '2-digit',
                        month: '2-digit',
                        year: 'numeric'
                    });
                    const timeStr = now.toLocaleTimeString('vi-VN', {
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit',
                        hour12: false
                    });

                    // Update time display for both live and offline states
                    $('#current-date, #current-date-offline').text(dateStr);
                    $('#current-time, #current-time-offline').text(timeStr);
                }, 1000);
            }

            // Start realtime clock immediately
            startRealtimeClock();

            // Initialize live timer if livestream is active
            @if(isset($webinar->livestreams["is_live"]) && $webinar->livestreams["is_live"]==1)
                liveStartTime = new Date().getTime();
            startLiveTimer();
            @endif

            // Counter animation function
            function animateCounter(element, start, end, duration = 1500) {
                const $element = $(element);
                const range = end - start;
                const increment = end > start ? 1 : -1;
                const stepTime = Math.abs(Math.floor(duration / range));
                let current = start;

                $element.addClass('updating');

                const timer = setInterval(() => {
                    current += increment;
                    if (element.includes('revenue')) {
                        $element.text(current.toLocaleString('vi-VN'));
                    } else if (element.includes('viewers') || element.includes('total-viewers')) {
                        $element.text(current > 1000 ? (current / 1000).toFixed(1) + 'k' : current);
                    } else {
                        $element.text(current.toLocaleString('vi-VN'));
                    }

                    if (current === end) {
                        clearInterval(timer);
                        setTimeout(() => $element.removeClass('updating'), 300);
                    }
                }, stepTime);
            }

            // Live timer function
            function startLiveTimer() {
                if (!liveStartTime) liveStartTime = new Date().getTime();

                liveTimer = setInterval(() => {
                    const now = new Date().getTime();
                    const elapsed = now - liveStartTime;

                    const hours = Math.floor(elapsed / (1000 * 60 * 60));
                    const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);

                    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    $('#live-timer').text(timeString);
                }, 1000);
            }

            // Stop live timer
            function stopLiveTimer() {
                if (liveTimer) {
                    clearInterval(liveTimer);
                    liveTimer = null;
                }
            }


            // Add hover effects for metric items
            $('.metric-item').hover(
                function () {
                    $(this).find('.metric-value').addClass('updating');
                },
                function () {
                    $(this).find('.metric-value').removeClass('updating');
                }
            );

            // Pulse effect for live badge
            if ($('.live-badge.active').length) {
                setInterval(() => {
                    $('.live-dot').css('animation', 'none');
                    setTimeout(() => {
                        $('.live-dot').css('animation', 'livePulse 1.5s infinite ease-in-out');
                    }, 10);
                }, 3000);
            }

            function fetchDataLiveStream() {
                $.ajax({
                    url: "{{route('webinars.get.data.livestream',$webinar)}}", // Thay bằng URL API của bạn
                    method: 'GET', // Hoặc 'POST' nếu cần
                    success: function (data) {
                        console.log(data.error)
                        if (!data.error) {
                            $('#live-viewers').text(data.data.total_view_online);
                            $('#total-viewers').text(data.data.total_view);
                            $('#total-orders').text(data.data.total_order_paid);
                            $('#total-orders-pending').text(data.data.total_order_pending);
                            $('#total-revenue').text(data.data.total_amount_order);
                        }
                    }
                });
            }
            @if(isset($webinar->livestreams["is_live"])&&$webinar->livestreams["is_live"]==1)
            // Gọi lại mỗi 5 giây (5000 milliseconds)
            setInterval(fetchDataLiveStream, 5000);
            @endif
        });
    </script>
@endpush

@push('styles')
    <style>
        .btn {
            position: relative;
            min-width: 80px;
        }

        .btn-text {
            font-size: 0.85rem;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            line-height: 1.2;
            display: block;
            text-align: center;
        }

        /* Analytics Dashboard Styles */
        .analytics-dashboard {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 24px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
            color: white;
        }

        .analytics-dashboard::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            pointer-events: none;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .revenue-section {
            display: flex;
            flex-direction: column;
        }

        .revenue-label {
            font-size: 16px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 8px;
            font-weight: 500;
            letter-spacing: 0.5px;
        }

        .revenue-amount {
            font-size: 48px;
            font-weight: 800;
            color: white;
            line-height: 1;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .live-info-section {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 12px;
        }

        .live-time-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            padding: 12px 16px;
            min-width: 200px;
        }

        .live-start-time {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .live-start-time i {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .live-duration-display {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .duration-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .live-timer {
            font-size: 20px;
            font-weight: 700;
            color: #4facfe;
            line-height: 1;
            font-family: 'Courier New', monospace;
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 6px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .offline-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 16px;
            min-width: 200px;
        }

        .next-live-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        .next-live-info i {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        .current-time-display {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
        }

        .current-time-display i {
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }

        .live-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            letter-spacing: 0.5px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .live-badge.active {
            background: rgba(46, 204, 113, 0.9);
            color: white;
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }

        .live-badge.inactive {
            background: rgba(149, 165, 166, 0.9);
            color: white;
        }

        .live-dot {
            width: 8px;
            height: 8px;
            background: white;
            border-radius: 50%;
            animation: livePulse 1.5s infinite ease-in-out;
        }

        .offline-dot {
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 50%;
        }

        @keyframes livePulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.3);
                opacity: 0.7;
            }
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 16px;
            padding: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .metric-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .metric-item:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .metric-item:hover::before {
            opacity: 1;
        }

        .metric-icon {
            font-size: 24px;
            margin-bottom: 12px;
            opacity: 0.9;
        }

        .metric-content {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .metric-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            line-height: 1.2;
        }

        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: white;
            line-height: 1;
            font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
        }

        /* Animation for number changes */
        .metric-value.updating {
            animation: numberPulse 0.3s ease-in-out;
        }

        @keyframes numberPulse {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
                color: #4facfe;
            }
            100% {
                transform: scale(1);
            }
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }

            .revenue-amount {
                font-size: 40px;
            }
        }

        @media (max-width: 992px) {
            .analytics-dashboard {
                padding: 30px;
            }

            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 15px;
            }

            .metric-value {
                font-size: 20px;
            }

            .revenue-amount {
                font-size: 36px;
            }
        }

        @media (max-width: 768px) {
            .d-flex.justify-content-between {
                flex-direction: column;
                align-items: stretch !important;
                gap: 1rem;
            }

            .d-flex.flex-wrap {
                justify-content: stretch;
            }

            .d-flex.flex-wrap .btn {
                flex: 1;
            }

            .btn-text {
                font-size: 0.75rem;
            }

            .analytics-dashboard {
                padding: 25px 20px;
                border-radius: 16px;
                margin: 0 -15px;
            }

            .dashboard-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: 15px;
                margin-bottom: 25px;
            }

            .live-info-section {
                flex-direction: column;
                align-items: center;
                width: 100%;
            }

            .live-time-info, .offline-info {
                min-width: auto;
                width: 100%;
                max-width: 300px;
            }

            .live-timer {
                font-size: 18px;
            }

            .metrics-grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 12px;
            }

            .metric-item {
                padding: 15px;
            }

            .metric-value {
                font-size: 16px;
            }

            .metric-label {
                font-size: 10px;
            }

            .revenue-amount {
                font-size: 32px;
            }

            .revenue-label {
                font-size: 14px;
            }
        }

        @media (max-width: 576px) {
            .analytics-dashboard {
                padding: 20px 15px;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .metric-item {
                padding: 12px;
            }

            .metric-value {
                font-size: 18px;
            }

            .metric-icon {
                font-size: 20px;
                margin-bottom: 8px;
            }

            .revenue-amount {
                font-size: 28px;
            }
        }

        /* Loading animation */
        .analytics-dashboard.loading {
            opacity: 0;
            transform: translateY(20px);
        }

        .analytics-dashboard.loaded {
            animation: dashboardFadeIn 0.8s ease-out forwards;
        }

        @keyframes dashboardFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Staggered animation for metrics */
        .metric-item {
            opacity: 0;
            transform: translateY(20px);
            animation: metricFadeIn 0.6s ease-out forwards;
        }

        .metric-item:nth-child(1) {
            animation-delay: 0.1s;
        }

        .metric-item:nth-child(2) {
            animation-delay: 0.2s;
        }

        .metric-item:nth-child(3) {
            animation-delay: 0.3s;
        }

        .metric-item:nth-child(4) {
            animation-delay: 0.4s;
        }

        .metric-item:nth-child(5) {
            animation-delay: 0.5s;
        }

        .metric-item:nth-child(6) {
            animation-delay: 0.6s;
        }

        .metric-item:nth-child(7) {
            animation-delay: 0.7s;
        }

        .metric-item:nth-child(8) {
            animation-delay: 0.8s;
        }

        .metric-item:nth-child(9) {
            animation-delay: 0.9s;
        }

        .metric-item:nth-child(10) {
            animation-delay: 1.0s;
        }

        @keyframes metricFadeIn {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
@endpush
