@extends('layouts.app')

@php
use Illuminate\Support\Facades\Schema;
use App\Models\Module;
@endphp

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="main-title m-0">
        <i class="fas fa-info-circle"></i> CHI TIẾT WEBINAR
    </h1>
    <div class="d-flex flex-wrap gap-2">
        @if(Module::isEnabled('livestream_education'))
            <a href="{{ route('webinars.livestream.class', $webinar) }}" class="btn btn-success d-flex flex-column align-items-center p-2">
                <i class="fas fa-rss mb-1 fs-5"></i>
                <span class="btn-text">LiveStream Lớp học</span>
            </a>
        @endif
        @if(Module::isEnabled('livestream_marketing'))
            <a href="{{ route('webinars.livestream', $webinar) }}" class="btn btn-success d-flex flex-column align-items-center p-2">
                <i class="fas fa-rss mb-1 fs-5"></i>
                <span class="btn-text">LiveStream</span>
            </a>
        @endif
        @can('update', $webinar)
            <a href="{{ route('webinars.edit', $webinar) }}" class="btn btn-primary d-flex flex-column align-items-center p-2">
                <i class="fas fa-edit mb-1 fs-5"></i>
                <span class="btn-text">Chỉnh sửa</span>
            </a>
            <a href="{{ route('webinars.upload', $webinar) }}" class="btn btn-success d-flex flex-column align-items-center p-2">
                <i class="fas fa-upload mb-1 fs-5"></i>
                <span class="btn-text">Tải video</span>
            </a>

            <a href="{{ route('webinars.analytics', $webinar) }}" class="btn btn-warning d-flex flex-column align-items-center p-2">
                <i class="fas fa-chart-line mb-1 fs-5"></i>
                <span class="btn-text">Phân tích</span>
                @if(!Schema::hasColumn('webinar_participants', 'view_duration'))
                <span class="badge bg-info position-absolute top-0 end-0" data-bs-toggle="tooltip" data-bs-placement="top" title="Tính năng đang được phát triển">Beta</span>
                @endif
            </a>
        @endcan

        <a href="{{ route('participants.index', $webinar) }}" class="btn btn-info d-flex flex-column align-items-center p-2">
            <i class="fas fa-users mb-1 fs-5"></i>
            <span class="btn-text">Người xem</span>
            <span class="badge bg-light text-dark position-absolute top-0 end-0">{{ $webinar->participants->count() }}</span>
        </a>

        <a href="{{ route('comments.by_schedule', $webinar) }}" class="btn btn-secondary d-flex flex-column align-items-center p-2">
            <i class="fas fa-comments mb-1 fs-5"></i>
            <span class="btn-text">Bình luận</span>
        </a>

        <a href="{{ route('webinars.index') }}" class="btn btn-light d-flex flex-column align-items-center p-2">
            <i class="fas fa-arrow-left mb-1 fs-5"></i>
            <span class="btn-text">Quay lại</span>
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h2 class="text-dark mb-3 fw-bold">
                    <i class="fas fa-video text-primary me-2"></i> Tên Webinar: {{ $webinar->title }}
                </h2>
                <p class="text-muted mb-4 d-flex align-items-center">
                    <i class="fas fa-user-tie text-primary me-2"></i> Người Thuyết Trình: <span class="fw-semibold ms-1">{{ $webinar->speaker }}</span>
                </p>

                <div class="card mb-4">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-link text-primary me-2"></i>
                        <h5 class="mb-0">Đường Dẫn Tham Gia</h5>
                    </div>
                    <div class="card-body">
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ $webinar->join_url }}" id="join-url" readonly>
                            <button class="btn btn-outline-primary copy-btn" type="button" data-clipboard-target="#join-url">
                                <i class="fas fa-copy me-1"></i> Sao Chép
                            </button>
                            @can('update', $webinar)
                                <button class="btn btn-outline-secondary" id="regenerate-url" type="button" title="Tạo đường dẫn mới">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            @endcan
                        </div>
                        @can('update', $webinar)
                            <div class="form-text mt-2">
                                <i class="fas fa-info-circle me-1"></i> Nhấn vào nút <i class="fas fa-sync-alt"></i> để tạo một đường dẫn tham gia mới.
                            </div>
                        @endcan

                        <!-- UTM Tracking Parameters -->
                        <div class="mt-3 border-top pt-3">
                            <p class="text-muted mb-2"><i class="fas fa-chart-line me-1"></i> <strong>Thêm tham số UTM để theo dõi</strong></p>
                            <div class="row g-2 mb-2">
                                <div class="col-md-6">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">utm_source</span>
                                        <input type="text" class="form-control" id="utm_source" placeholder="facebook, google, email...">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">utm_medium</span>
                                        <input type="text" class="form-control" id="utm_medium" placeholder="cpc, banner, email...">
                                    </div>
                                </div>
                            </div>
                            <div class="row g-2 mb-2">
                                <div class="col-md-6">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">utm_campaign</span>
                                        <input type="text" class="form-control" id="utm_campaign" placeholder="summer_sale, webinar_promo...">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">utm_content</span>
                                        <input type="text" class="form-control" id="utm_content" placeholder="banner_1, footer_link...">
                                    </div>
                                </div>
                            </div>
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">utm_term</span>
                                        <input type="text" class="form-control" id="utm_term" placeholder="running+shoes, marketing+webinar...">
                                    </div>
                                </div>
                                <div class="col-md-6 d-flex align-items-end">
                                    <button class="btn btn-sm btn-primary w-100" id="generate-utm-url">
                                        <i class="fas fa-magic me-1"></i> Tạo đường dẫn với UTM
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="stats-card bg-primary-light border-0 rounded">
                            <div class="stats-icon text-primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <h6 class="card-title text-primary">SỐ NGƯỜI XEM ẢO</h6>
                            <h3 class="stats-value text-primary">{{ $webinar->virtual_viewers }}</h3>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="stats-card bg-success-light border-0 rounded">
                            <div class="stats-icon text-success">
                                <i class="fas fa-clock"></i>
                            </div>
                            <h6 class="card-title text-success">THỜI GIAN CHỜ</h6>
                            <h3 class="stats-value text-success">{{ $webinar->waiting_time }} phút</h3>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-primary">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-calendar-alt me-2"></i> Lịch Trình Webinar
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($webinar->schedules && count($webinar->schedules) > 0)
                            <ul class="list-group list-group-flush">
                                @foreach($webinar->schedules as $index => $schedule)
                                    @php
                                        $scheduledTime = \Carbon\Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                                        $now = \Carbon\Carbon::now();

                                        // So sánh ngày và giờ của sự kiện với thời gian hiện tại
                                        // Chỉ đánh dấu "Đã qua" khi ngày hôm nay vượt qua ngày sự kiện
                                        // hoặc nếu cùng ngày thì giờ hiện tại phải lớn hơn giờ sự kiện ít nhất 1 giờ
                                        if ($scheduledTime->format('Y-m-d') < $now->format('Y-m-d')) {
                                            $isPast = true; // Ngày đã qua
                                        } elseif ($scheduledTime->format('Y-m-d') == $now->format('Y-m-d')
                                                && (int)$scheduledTime->format('H') < (int)$now->format('H')) {
                                            $isPast = true; // Cùng ngày nhưng giờ đã qua
                                        } else {
                                            $isPast = false; // Chưa qua
                                        }
                                    @endphp
                                    <li class="list-group-item border-bottom py-3 {{ $isPast ? 'text-muted' : '' }}">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="d-flex align-items-center mb-1">
                                                    <i class="fas fa-calendar-day text-primary me-2"></i>
                                                    <span class="fw-medium">{{ \Carbon\Carbon::parse($schedule['date'])->format('d/m/Y') }}</span>
                                                </div>
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-clock text-primary me-2"></i>
                                                    <span>{{ \Carbon\Carbon::parse($schedule['time'])->format('H:i') }}</span>
                                                </div>
                                            </div>
                                            <div>
                                                @if($isPast)
                                                    <span class="badge bg-secondary">Đã qua</span>
                                                @else
                                                    <span class="badge bg-primary">Sắp diễn ra</span>
                                                @endif
                                            </div>
                                        </div>
                                    </li>
                                @endforeach
                            </ul>
                        @else
                            <div class="alert alert-info d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>Chưa có lịch trình nào được thiết lập.</span>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header bg-secondary">
                        <h5 class="mb-0 d-flex align-items-center">
                            <i class="fas fa-comments me-2"></i> Bình Luận Seeding
                        </h5>
                    </div>
                    <div class="card-body">
                        @if($webinar->seeded_comments && count($webinar->seeded_comments) > 0)
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>Thời gian</th>
                                            <th>Tên</th>
                                            <th>Nội dung</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($webinar->seeded_comments as $comment)
                                            <tr>
                                                <td>{{ $comment['time'] ?? '00:00:00' }}</td>
                                                <td>{{ $comment['name'] ?? 'Không tên' }}</td>
                                                <td>{{ $comment['content'] ?? '' }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-3">
                                <a href="{{ route('webinars.edit', $webinar) }}#comments" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit me-1"></i> Chỉnh sửa bình luận seeding
                                </a>
                            </div>
                        @else
                            <div class="alert alert-info d-flex align-items-center">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>Chưa có bình luận seeding nào được thiết lập.</span>
                            </div>
                            <a href="{{ route('webinars.edit', $webinar) }}#comments" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-plus me-1"></i> Thêm bình luận seeding
                            </a>
                        @endif
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <div class="mb-4">
                    <h5 class="d-flex align-items-center mb-3">
                        <i class="fas fa-film text-primary me-2"></i> Trạng Thái Video
                    </h5>

                    @if($webinar->video_path)
                        <div class="alert alert-success d-flex align-items-center mb-3">
                            <i class="fas fa-check-circle me-2"></i>
                            <span>Video đã được tải lên thành công</span>
                        </div>

                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-play-circle text-primary me-2"></i> Xem Trước Video</h6>
                                @can('update', $webinar)
                                    <a href="{{ route('webinars.upload', $webinar) }}" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit me-1"></i> Thay Thế Video
                                    </a>
                                @endcan
                            </div>
                            <div class="card-body p-2">
                                <div class="ratio ratio-16x9">
                                    <video controls class="rounded shadow-sm">
                                        @if($webinar->s3_url)
                                            @php
                                                // Fix S3 URL if it doesn't contain the bucket name
                                                $s3Url = $webinar->s3_url;
                                                $bucket = config('filesystems.disks.vns3.bucket');

                                                // Check if URL already contains bucket name
                                                if (!str_contains($s3Url, '/' . $bucket . '/') && str_contains($s3Url, '/webinar-')) {
                                                    // Insert bucket name into URL
                                                    $baseUrl = substr($s3Url, 0, strpos($s3Url, '/webinar-'));
                                                    $path = substr($s3Url, strpos($s3Url, '/webinar-'));
                                                    $s3Url = $baseUrl . '/' . $bucket . $path;
                                                }
                                            @endphp
                                            <source src="{{ $s3Url }}" type="video/mp4">
                                        @elseif(strpos($webinar->video_path, 's3://') === 0)
                                            {{-- Skip if s3 path but no URL --}}
                                        @else
                                            <source src="{{ Storage::url(str_replace('public/', '', $webinar->video_path)) }}" type="video/mp4">
                                        @endif
                                        Trình duyệt của bạn không hỗ trợ thẻ video.
                                    </video>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="alert alert-warning d-flex align-items-center mb-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <span>Chưa có video nào được tải lên</span>

                            @can('update', $webinar)
                                <a href="{{ route('webinars.upload', $webinar) }}" class="btn btn-sm btn-success ms-auto">
                                    <i class="fas fa-upload me-1"></i> Tải Lên Ngay
                                </a>
                            @endcan
                        </div>
                    @endif
                </div>

                <div class="mb-4">
                    <h5 class="d-flex align-items-center mb-3">
                        <i class="fas fa-cog text-primary me-2"></i> Cài đặt Form Đăng ký
                    </h5>

                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h6 class="mb-0">Thông tin người tham gia yêu cầu</h6>
                            @can('update', $webinar)
                                <a href="{{ route('webinars.edit', $webinar) }}#join-settings" class="btn btn-sm btn-primary">
                                    <i class="fas fa-edit me-1"></i> Cài đặt
                                </a>
                            @endcan
                        </div>
                        <div class="card-body">
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item border-bottom py-2 d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-user text-primary me-2"></i> Họ và tên
                                    </div>
                                    <div>
                                        @if($webinar->join_settings['name_required'])
                                            <span class="badge bg-primary">Bắt buộc</span>
                                        @else
                                            <span class="badge bg-secondary">Tùy chọn</span>
                                        @endif
                                    </div>
                                </li>
                                <li class="list-group-item border-bottom py-2 d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-phone text-primary me-2"></i> Số điện thoại
                                    </div>
                                    <div>
                                        @if($webinar->join_settings['phone_required'])
                                            <span class="badge bg-primary">Bắt buộc</span>
                                        @else
                                            <span class="badge bg-secondary">Tùy chọn</span>
                                        @endif
                                    </div>
                                </li>
                                <li class="list-group-item py-2 d-flex justify-content-between align-items-center">
                                    <div>
                                        <i class="fas fa-envelope text-primary me-2"></i> Email
                                    </div>
                                    <div>
                                        @if($webinar->join_settings['email_required'])
                                            <span class="badge bg-primary">Bắt buộc</span>
                                        @else
                                            <span class="badge bg-secondary">Tùy chọn</span>
                                        @endif
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var clipboard = new ClipboardJS('.copy-btn');

        clipboard.on('success', function(e) {
            let button = e.trigger;
            let originalHTML = button.innerHTML;

            button.innerHTML = '<i class="fas fa-check"></i> Đã sao chép!';

            // Hiển thị thông báo
            Swal.fire({
                title: 'Đã sao chép!',
                text: 'Đường dẫn đã được sao chép vào clipboard',
                icon: 'success',
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });

            setTimeout(function() {
                button.innerHTML = originalHTML;
            }, 2000);

            e.clearSelection();
        });

        // Xử lý nút tạo mới đường dẫn tham gia
        const regenerateUrlBtn = document.getElementById('regenerate-url');
        if (regenerateUrlBtn) {
            regenerateUrlBtn.addEventListener('click', function() {
                // Hiển thị xác nhận trước khi tạo mới
                Swal.fire({
                    title: 'Xác nhận tạo mới?',
                    text: 'Đường dẫn tham gia cũ sẽ không còn hoạt động sau khi tạo mới. Bạn có chắc chắn muốn tiếp tục?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#fa8128',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Tạo mới',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Hiển thị loading
                        Swal.fire({
                            title: 'Đang xử lý...',
                            html: 'Vui lòng đợi trong giây lát',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Gửi request AJAX để tạo mới đường dẫn
                        fetch('{{ route('webinars.regenerate-url', $webinar) }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            credentials: 'same-origin'
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Cập nhật giá trị input
                                document.getElementById('join-url').value = data.join_url;

                                // Hiển thị thông báo thành công
                                Swal.fire({
                                    title: 'Thành công',
                                    text: data.message,
                                    icon: 'success',
                                    confirmButtonText: 'Đã hiểu'
                                });
                            } else {
                                Swal.fire({
                                    title: 'Lỗi',
                                    text: data.message,
                                    icon: 'error',
                                    confirmButtonText: 'Đã hiểu'
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            Swal.fire({
                                title: 'Lỗi',
                                text: 'Đã xảy ra lỗi khi tạo đường dẫn mới',
                                icon: 'error',
                                confirmButtonText: 'Đã hiểu'
                            });
                        });
                    }
                });
            });
        }

        // Xử lý tạo đường dẫn với UTM parameters
        const generateUtmUrlBtn = document.getElementById('generate-utm-url');
        if (generateUtmUrlBtn) {
            generateUtmUrlBtn.addEventListener('click', function() {
                const baseUrl = document.getElementById('join-url').value;
                const utmSource = document.getElementById('utm_source').value;
                const utmMedium = document.getElementById('utm_medium').value;
                const utmCampaign = document.getElementById('utm_campaign').value;
                const utmContent = document.getElementById('utm_content').value;
                const utmTerm = document.getElementById('utm_term').value;

                // Tạo URL với các tham số UTM
                let utmUrl = baseUrl;
                const params = [];

                if (utmSource) params.push(`utm_source=${encodeURIComponent(utmSource)}`);
                if (utmMedium) params.push(`utm_medium=${encodeURIComponent(utmMedium)}`);
                if (utmCampaign) params.push(`utm_campaign=${encodeURIComponent(utmCampaign)}`);
                if (utmContent) params.push(`utm_content=${encodeURIComponent(utmContent)}`);
                if (utmTerm) params.push(`utm_term=${encodeURIComponent(utmTerm)}`);

                if (params.length > 0) {
                    // Kiểm tra xem URL đã có tham số chưa
                    utmUrl += (baseUrl.includes('?') ? '&' : '?') + params.join('&');
                }

                // Cập nhật giá trị input và chọn nó để dễ sao chép
                const joinUrlInput = document.getElementById('join-url');
                joinUrlInput.value = utmUrl;
                joinUrlInput.select();

                try {
                    // Sao chép vào clipboard
                    document.execCommand('copy');

                    // Hiển thị thông báo
                    Swal.fire({
                        title: 'Đã tạo URL với UTM!',
                        text: 'Đường dẫn mới đã được sao chép vào clipboard',
                        icon: 'success',
                        toast: true,
                        position: 'top-end',
                        showConfirmButton: false,
                        timer: 3000
                    });
                } catch (err) {
                    console.error('Không thể sao chép:', err);
                }
            });
        }
    });
</script>
@endpush

@push('styles')
<style>
    .btn {
        position: relative;
        min-width: 80px;
    }

    .btn-text {
        font-size: 0.85rem;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.2;
        display: block;
        text-align: center;
    }

    @media (max-width: 768px) {
        .d-flex.justify-content-between {
            flex-direction: column;
            align-items: stretch !important;
            gap: 1rem;
        }

        .d-flex.flex-wrap {
            justify-content: stretch;
        }

        .d-flex.flex-wrap .btn {
            flex: 1;
        }

        .btn-text {
            font-size: 0.75rem;
        }
    }
</style>
@endpush
