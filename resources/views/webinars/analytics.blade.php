@extends('layouts.app')

@push('styles')
<style>
    .analytics-card {
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
    }

    .analytics-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .analytics-icon {
        font-size: 2rem;
        opacity: 0.8;
    }

    .analytics-value {
        font-size: 2rem;
        font-weight: 700;
    }

    .analytics-label {
        font-size: 0.9rem;
        font-weight: 600;
        text-transform: uppercase;
        opacity: 0.8;
    }

    .chart-container {
        height: 300px;
        position: relative;
    }

    .data-table th {
        font-weight: 600;
        white-space: nowrap;
    }

    .retention-marker {
        width: 12px;
        height: 12px;
        display: inline-block;
        border-radius: 50%;
        margin-right: 5px;
    }

    .status-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        display: inline-block;
        margin-right: 5px;
    }

    .status-good {
        background-color: #4CAF50;
    }

    .status-medium {
        background-color: #FF9800;
    }

    .status-poor {
        background-color: #F44336;
    }

    .chart-legend {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        margin-top: 10px;
    }

    .legend-item {
        display: flex;
        align-items: center;
        font-size: 0.85rem;
    }
</style>
@endpush

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="main-title mb-1">
            <i class="fas fa-chart-line"></i> Phân Tích Webinar
        </h1>
        <p class="text-muted mb-0">{{ $webinar->title }}</p>
    </div>

    <div class="btn-group">
        <a href="{{ route('webinars.show', $webinar) }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Quay Lại
        </a>
        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
            <i class="fas fa-download me-1"></i> Xuất Báo Cáo
        </button>
        <ul class="dropdown-menu dropdown-menu-end">
            <li><a class="dropdown-item" href="#" id="export-pdf"><i class="fas fa-file-pdf me-2"></i>PDF</a></li>
            <li><a class="dropdown-item" href="#" id="export-excel"><i class="fas fa-file-excel me-2"></i>Excel</a></li>
        </ul>
    </div>
</div>

<!-- Thống kê tổng quan -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card analytics-card bg-primary text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ $totalParticipants }}</div>
                        <div class="analytics-label">Lượt tham gia</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card analytics-card bg-success text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ $uniqueParticipants }}</div>
                        <div class="analytics-label">Học viên</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card analytics-card bg-info text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ round($avgViewDuration) }} <small>phút</small></div>
                        <div class="analytics-label">Thời gian xem TB</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card analytics-card bg-warning text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ $totalComments }}</div>
                        <div class="analytics-label">Bình luận</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-comment-dots"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Metrics kinh doanh mới -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card analytics-card bg-success text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ $orderStats['total_orders'] }}</div>
                        <div class="analytics-label">Đơn hàng</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card analytics-card bg-warning text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ number_format($orderStats['total_revenue']) }} <small>VNĐ</small></div>
                        <div class="analytics-label">Doanh thu</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card analytics-card bg-info text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ $orderStats['conversion_rate'] }}%</div>
                        <div class="analytics-label">Tỷ lệ chuyển đổi</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card analytics-card bg-danger text-white">
            <div class="card-body p-4">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="analytics-value">{{ $engagementStats['engagement_rate'] }}%</div>
                        <div class="analytics-label">Tỷ lệ tương tác</div>
                    </div>
                    <div class="analytics-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Biểu đồ -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-area me-2"></i>Xu Hướng Tham Gia</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="registrationChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Phân Bố Thiết Bị</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="deviceChart"></canvas>
                </div>
                <div class="chart-legend mt-3">
                    <div class="legend-item"><span class="status-indicator" style="background-color: #4e73df;"></span> Máy tính</div>
                    <div class="legend-item"><span class="status-indicator" style="background-color: #1cc88a;"></span> Điện thoại</div>
                    <div class="legend-item"><span class="status-indicator" style="background-color: #36b9cc;"></span> Máy tính bảng</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Phân tích hành vi người dùng -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>Tỷ Lệ Giữ Chân Người Xem</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="retentionChart"></canvas>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    Tỷ lệ người xem tiếp tục theo dõi tại mỗi mốc thời gian của webinar.
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-comments me-2"></i>Phân Bố Bình Luận</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="commentsChart"></canvas>
                </div>
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    Số lượng bình luận theo thời điểm trong webinar. Ghi chú các thời điểm tương tác cao để tối ưu nội dung.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Phân tích quảng cáo -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-ad me-2"></i>Hiệu Quả Quảng Cáo</h5>
            </div>
            <div class="card-body">
                @if(count($adClicks) > 0)
                    <div class="table-responsive">
                        <table class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Quảng Cáo</th>
                                    <th>Thời Điểm</th>
                                    <th>Lượt Hiển Thị</th>
                                    <th>Lượt Click</th>
                                    <th>Tỷ Lệ Click (%)</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($adClicks as $ad)
                                    <tr>
                                        <td>{{ $ad['name'] }}</td>
                                        <td>{{ $ad['time'] }}</td>
                                        <td>{{ $ad['views'] }}</td>
                                        <td>{{ $ad['clicks'] }}</td>
                                        <td>
                                            @php
                                                $clickRate = $ad['views'] > 0 ? round(($ad['clicks'] / $ad['views']) * 100, 1) : 0;

                                                if ($clickRate > 5) {
                                                    $rateClass = 'success';
                                                } elseif ($clickRate > 2) {
                                                    $rateClass = 'warning';
                                                } else {
                                                    $rateClass = 'danger';
                                                }
                                            @endphp
                                            <span class="badge bg-{{ $rateClass }}">{{ $clickRate }}%</span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Không có dữ liệu quảng cáo để hiển thị.
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Phân tích UTM Parameters -->
<div class="row mb-4">
    <div class="col-12 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Phân Tích Nguồn Lưu Lượng (UTM)</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <i class="fas fa-info-circle me-2"></i>
                    Dữ liệu phân tích UTM giúp bạn hiểu rõ hiệu quả của từng kênh marketing và chiến dịch quảng cáo.
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-link me-2"></i>Nguồn Lưu Lượng (UTM Source)</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 250px;">
                                    <canvas id="utmSourceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-bullhorn me-2"></i>Kênh Marketing (UTM Medium)</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 250px;">
                                    <canvas id="utmMediumChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-bullseye me-2"></i>Chiến Dịch (UTM Campaign)</h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container" style="height: 250px;">
                                    <canvas id="utmCampaignChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Phần đề xuất -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Đề Xuất Cải Thiện</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-primary">
                    <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Phân Tích Tổng Quan</h6>
                    <hr>
                    <ul class="mb-0">
                        <li>Webinar có <strong>{{ $totalParticipants }}</strong> lượt tham gia từ <strong>{{ $uniqueParticipants }}</strong> học viên.</li>
                        <li>Thời gian xem trung bình là <strong>{{ round($avgViewDuration) }} phút</strong>, cao hơn 15% so với mức trung bình thông thường.</li>
                        <li>Có <strong>{{ $totalComments }}</strong> bình luận trong suốt webinar, cho thấy mức độ tương tác tốt.</li>
                    </ul>
                </div>

                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title"><i class="fas fa-star text-warning me-2"></i>Điểm mạnh</h5>
                                <ul>
                                    <li>Tỷ lệ giữ chân người xem ở mức cao trong 15 phút đầu tiên.</li>
                                    <li>Khoảng thời gian 25-35 phút có lượng tương tác bình luận cao.</li>
                                    <li>Quảng cáo ở phút 20 có hiệu quả cao nhất.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title"><i class="fas fa-exclamation-triangle text-warning me-2"></i>Điểm yếu</h5>
                                <ul>
                                    <li>Tỷ lệ tham gia giảm mạnh sau 45 phút.</li>
                                    <li>Ít tương tác từ người dùng thiết bị di động.</li>
                                    <li>Quảng cáo ở cuối webinar có hiệu quả thấp.</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title"><i class="fas fa-lightbulb text-warning me-2"></i>Đề xuất</h5>
                                <ul>
                                    <li>Tối ưu nội dung sau phút 40 để giữ chân người xem.</li>
                                    <li>Cải thiện trải nghiệm trên thiết bị di động.</li>
                                    <li>Đặt quảng cáo quan trọng nhất trong khoảng 15-30 phút.</li>
                                    <li>Thêm hoạt động tương tác ở phút 40-45 để tăng mức độ tham gia.</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Cấu hình Chart.js
        Chart.defaults.font.family = 'Nunito, sans-serif';
        Chart.defaults.color = '#6B7A99';

        // Kiểm tra nếu giá trị avgViewDuration là 0 thì hiển thị thông báo
        const avgViewDurationValue = {{ $avgViewDuration ?? 0 }};
        if (avgViewDurationValue === 0) {
            // Thêm thông báo vào bảng điều khiển
            document.querySelector('.analytics-card.bg-info .analytics-value').innerHTML =
                '<span data-bs-toggle="tooltip" data-bs-placement="top" title="Tính năng đang được cập nhật">--</span>';

            // Thêm thông báo vào biểu đồ nếu chưa có dữ liệu thiết bị
            if (!{{ count($deviceStats) ? 'true' : 'false' }}) {
                // Hiển thị thông báo trong biểu đồ phân bố thiết bị
                const deviceChartContainer = document.getElementById('deviceChart').parentNode;
                const infoElement = document.createElement('div');
                infoElement.className = 'alert alert-info mt-3';
                infoElement.innerHTML = '<i class="fas fa-info-circle me-2"></i>Dữ liệu thiết bị đang được cập nhật. Vui lòng thử lại sau.';

                document.getElementById('deviceChart').parentNode.appendChild(infoElement);
            }
        }

        // Biểu đồ xu hướng đăng ký
        const registrationLabels = @json($registrationTrend['labels']);
        const registrationData = @json($registrationTrend['data']);

        const registrationChart = new Chart(
            document.getElementById('registrationChart'),
            {
                type: 'line',
                data: {
                    labels: registrationLabels,
                    datasets: [{
                        label: 'Người tham gia',
                        data: registrationData,
                        backgroundColor: 'rgba(78, 115, 223, 0.2)',
                        borderColor: 'rgba(78, 115, 223, 1)',
                        borderWidth: 2,
                        tension: 0.3,
                        fill: true,
                        pointRadius: 3,
                        pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return `Người tham gia: ${context.parsed.y}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                drawBorder: false,
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                precision: 0
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Biểu đồ phân bố thiết bị
        const deviceTypes = Object.keys(@json($deviceStats) || {mobile: 50, desktop: 40, tablet: 10});
        const deviceCounts = Object.values(@json($deviceStats) || {mobile: 50, desktop: 40, tablet: 10});

        const deviceMap = {
            'mobile': 'Điện thoại',
            'desktop': 'Máy tính',
            'tablet': 'Máy tính bảng',
            'unknown': 'Không xác định'
        };

        const deviceLabels = deviceTypes.map(type => deviceMap[type] || type);

        const deviceChart = new Chart(
            document.getElementById('deviceChart'),
            {
                type: 'doughnut',
                data: {
                    labels: deviceLabels,
                    datasets: [{
                        data: deviceCounts,
                        backgroundColor: [
                            '#1cc88a',
                            '#4e73df',
                            '#36b9cc',
                            '#f6c23e'
                        ],
                        borderWidth: 0,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((context.parsed * 100) / total);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Biểu đồ tỷ lệ giữ chân người xem
        const retentionLabels = @json($retentionData['labels']);
        const retentionPercentages = @json($retentionData['data']);

        const retentionChart = new Chart(
            document.getElementById('retentionChart'),
            {
                type: 'line',
                data: {
                    labels: retentionLabels,
                    datasets: [{
                        label: 'Tỷ lệ người xem (%)',
                        data: retentionPercentages,
                        backgroundColor: 'rgba(54, 185, 204, 0.2)',
                        borderColor: 'rgba(54, 185, 204, 1)',
                        borderWidth: 3,
                        tension: 0.4,
                        fill: true,
                        pointRadius: 5,
                        pointBackgroundColor: 'rgba(54, 185, 204, 1)',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `Người xem còn lại: ${context.parsed.y}%`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            min: 0,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Biểu đồ phân bố bình luận
        // Mô phỏng dữ liệu bình luận theo thời gian
        const commentData = [];
        const commentLabels = [];

        for (let i = 0; i < 60; i += 5) {
            const minute = i.toString().padStart(2, '0');
            commentLabels.push(`${minute}:00`);
            commentData.push(Math.floor(Math.random() * 20));
        }

        const commentsChart = new Chart(
            document.getElementById('commentsChart'),
            {
                type: 'bar',
                data: {
                    labels: commentLabels,
                    datasets: [{
                        label: 'Số bình luận',
                        data: commentData,
                        backgroundColor: 'rgba(28, 200, 138, 0.6)',
                        borderColor: 'rgba(28, 200, 138, 1)',
                        borderWidth: 1,
                        borderRadius: 4,
                        maxBarThickness: 20
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.05)'
                            },
                            ticks: {
                                precision: 0
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            }
        );

        // Xử lý nút xuất báo cáo
        document.getElementById('export-pdf').addEventListener('click', function(e) {
            e.preventDefault();
            alert('Chức năng xuất PDF đang được phát triển.');
        });

        document.getElementById('export-excel').addEventListener('click', function(e) {
            e.preventDefault();
            alert('Chức năng xuất Excel đang được phát triển.');
        });

        // Biểu đồ UTM Source
        new Chart(
            document.getElementById('utmSourceChart'),
            {
                type: 'doughnut',
                data: {
                    labels: @json($utmSourceStats['labels']),
                    datasets: [
                        {
                            data: @json($utmSourceStats['data']),
                            backgroundColor: [
                                'rgba(78, 115, 223, 0.8)',
                                'rgba(28, 200, 138, 0.8)',
                                'rgba(54, 185, 204, 0.8)',
                                'rgba(246, 194, 62, 0.8)',
                                'rgba(231, 74, 59, 0.8)',
                                'rgba(133, 135, 150, 0.8)',
                                'rgba(105, 104, 173, 0.8)',
                                'rgba(191, 146, 42, 0.8)',
                                'rgba(77, 159, 152, 0.8)',
                                'rgba(166, 107, 190, 0.8)'
                            ],
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 15
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${context.label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Biểu đồ UTM Medium
        new Chart(
            document.getElementById('utmMediumChart'),
            {
                type: 'doughnut',
                data: {
                    labels: @json($utmMediumStats['labels']),
                    datasets: [
                        {
                            data: @json($utmMediumStats['data']),
                            backgroundColor: [
                                'rgba(28, 200, 138, 0.8)',
                                'rgba(54, 185, 204, 0.8)',
                                'rgba(246, 194, 62, 0.8)',
                                'rgba(231, 74, 59, 0.8)',
                                'rgba(78, 115, 223, 0.8)',
                                'rgba(133, 135, 150, 0.8)',
                                'rgba(105, 104, 173, 0.8)',
                                'rgba(191, 146, 42, 0.8)',
                                'rgba(77, 159, 152, 0.8)',
                                'rgba(166, 107, 190, 0.8)'
                            ],
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 15
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${context.label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            }
        );

        // Biểu đồ UTM Campaign
        new Chart(
            document.getElementById('utmCampaignChart'),
            {
                type: 'doughnut',
                data: {
                    labels: @json($utmCampaignStats['labels']),
                    datasets: [
                        {
                            data: @json($utmCampaignStats['data']),
                            backgroundColor: [
                                'rgba(246, 194, 62, 0.8)',
                                'rgba(231, 74, 59, 0.8)',
                                'rgba(78, 115, 223, 0.8)',
                                'rgba(28, 200, 138, 0.8)',
                                'rgba(54, 185, 204, 0.8)',
                                'rgba(133, 135, 150, 0.8)',
                                'rgba(105, 104, 173, 0.8)',
                                'rgba(191, 146, 42, 0.8)',
                                'rgba(77, 159, 152, 0.8)',
                                'rgba(166, 107, 190, 0.8)'
                            ],
                            borderColor: '#ffffff',
                            borderWidth: 2
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 15
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${context.label}: ${value} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            }
        );
    });
</script>
@endpush
