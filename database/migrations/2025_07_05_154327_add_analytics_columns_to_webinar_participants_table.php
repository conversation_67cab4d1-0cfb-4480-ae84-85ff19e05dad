<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('webinar_participants', function (Blueprint $table) {
            // Đ<PERSON><PERSON> bảo các cột analytics cần thiết tồn tại
            if (!Schema::hasColumn('webinar_participants', 'view_duration')) {
                $table->integer('view_duration')->nullable()->comment('Thời gian xem webinar tính bằng giây');
            }

            if (!Schema::hasColumn('webinar_participants', 'device_type')) {
                $table->string('device_type', 20)->nullable()->comment('Loại thiết bị: desktop, mobile, tablet');
            }

            if (!Schema::hasColumn('webinar_participants', 'join_count')) {
                $table->integer('join_count')->default(1)->comment('Số lần tham gia');
            }

            // Thêm các cột UTM nếu chưa có
            if (!Schema::hasColumn('webinar_participants', 'utm_source')) {
                $table->string('utm_source')->nullable();
            }

            if (!Schema::hasColumn('webinar_participants', 'utm_medium')) {
                $table->string('utm_medium')->nullable();
            }

            if (!Schema::hasColumn('webinar_participants', 'utm_campaign')) {
                $table->string('utm_campaign')->nullable();
            }

            if (!Schema::hasColumn('webinar_participants', 'utm_term')) {
                $table->string('utm_term')->nullable();
            }

            if (!Schema::hasColumn('webinar_participants', 'utm_content')) {
                $table->string('utm_content')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('webinar_participants', function (Blueprint $table) {
            // Không xóa các cột vì có thể đã tồn tại từ trước
        });
    }
};
