<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Webinar;
use App\Models\WebinarParticipant;
use App\Models\WebinarComment;
use App\Models\Order;
use App\Models\Question;
use App\Models\QuestionResponse;
use Carbon\Carbon;

class WebinarAnalyticsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Lấy webinar đầu tiên hoặc tạo mới
        $webinar = Webinar::first();
        if (!$webinar) {
            $webinar = Webinar::create([
                'user_id' => 1,
                'title' => 'Demo Webinar Analytics',
                'speaker' => 'Demo Speaker',
                'webinar_type' => 'live',
                'join_code' => 'demo123',
                'join_url' => 'demo-webinar',
                'video_duration_minutes' => 60,
                'created_at' => Carbon::now()->subDays(7),
            ]);
        }

        // Tạo participants với dữ liệu analytics
        $this->createParticipants($webinar);

        // <PERSON><PERSON><PERSON> comments
        $this->createComments($webinar);

        // Tạo orders
        $this->createOrders($webinar);

        // Tạo questions và responses
        $this->createQuestionsAndResponses($webinar);

        $this->command->info('Analytics demo data created successfully!');
    }

    private function createParticipants($webinar)
    {
        $utmSources = ['facebook', 'google', 'email', 'direct', 'youtube'];
        $utmMediums = ['social', 'cpc', 'email', 'organic', 'video'];
        $utmCampaigns = ['summer-sale', 'webinar-promo', 'retargeting', 'brand-awareness'];
        $devices = ['desktop', 'mobile', 'tablet'];

        for ($i = 1; $i <= 150; $i++) {
            WebinarParticipant::create([
                'webinar_id' => $webinar->id,
                'name' => 'Participant ' . $i,
                'email' => rand(1, 100) <= 70 ? "participant{$i}@example.com" : null, // 70% có email
                'phone' => rand(1, 100) <= 60 ? "098765432{$i}" : null, // 60% có phone
                'ip_address' => '192.168.1.' . rand(1, 254),
                'joined_at' => Carbon::now()->subDays(rand(0, 7))->subHours(rand(0, 23)),
                'join_count' => rand(1, 100) <= 20 ? rand(2, 5) : 1, // 20% repeat visitors
                'view_duration' => rand(300, 3600), // 5 phút đến 1 giờ
                'device_type' => $devices[array_rand($devices)],
                'utm_source' => rand(1, 100) <= 80 ? $utmSources[array_rand($utmSources)] : null,
                'utm_medium' => rand(1, 100) <= 80 ? $utmMediums[array_rand($utmMediums)] : null,
                'utm_campaign' => rand(1, 100) <= 60 ? $utmCampaigns[array_rand($utmCampaigns)] : null,
            ]);
        }
    }

    private function createComments($webinar)
    {
        $participants = $webinar->participants()->take(50)->get();

        foreach ($participants as $participant) {
            if (rand(1, 100) <= 40) { // 40% participants comment
                for ($j = 1; $j <= rand(1, 3); $j++) {
                    WebinarComment::create([
                        'webinar_id' => $webinar->id,
                        'participant_id' => $participant->id,
                        'name' => $participant->name,
                        'content' => 'This is a demo comment ' . $j,
                        'video_timestamp' => rand(0, 3600),
                        'created_at' => Carbon::now()->subDays(rand(0, 7)),
                    ]);
                }
            }
        }
    }

    private function createOrders($webinar)
    {
        $participants = $webinar->participants()->take(20)->get(); // 20 orders from 150 participants = ~13% conversion

        foreach ($participants as $participant) {
            Order::create([
                'webinar_id' => $webinar->id,
                'name' => $participant->name,
                'email' => $participant->email ?: '<EMAIL>',
                'phone' => $participant->phone ?: '0987654321',
                'product_name' => 'Demo Product',
                'price' => rand(500000, 2000000), // 500k - 2M VND
                'purchase_time' => Carbon::now()->subDays(rand(0, 7)),
                'order_type' => 'form',
                'payment_status' => rand(1, 100) <= 80 ? 'paid' : 'pending', // 80% paid
                'care_status' => ['new', 'contacted', 'interested', 'converted'][array_rand(['new', 'contacted', 'interested', 'converted'])],
            ]);
        }
    }

    private function createQuestionsAndResponses($webinar)
    {
        // Tạo questions
        $questions = [];
        for ($i = 1; $i <= 5; $i++) {
            $question = Question::create([
                'webinar_id' => $webinar->id,
                'title' => "Demo Question {$i}",
                'answers' => json_encode(['Option A', 'Option B', 'Option C', 'Option D']),
                'type' => ['multiple-choice', 'single-choice', 'rating'][array_rand(['multiple-choice', 'single-choice', 'rating'])],
                'status' => true,
            ]);
            $questions[] = $question;
        }

        // Tạo responses
        $participants = $webinar->participants()->take(80)->get(); // 80 participants respond

        foreach ($participants as $participant) {
            foreach ($questions as $question) {
                if (rand(1, 100) <= 60) { // 60% response rate per question
                    QuestionResponse::create([
                        'question_id' => $question->id,
                        'webinar_id' => $webinar->id,
                        'user_name' => $participant->name,
                        'user_email' => $participant->email,
                        'session_id' => 'session_' . $participant->id,
                        'selected_answers' => json_encode([rand(0, 3)]),
                        'rating' => $question->type === 'rating' ? rand(1, 5) : null,
                        'responded_at' => Carbon::now()->subDays(rand(0, 7)),
                        'ip_address' => $participant->ip_address,
                    ]);
                }
            }
        }
    }
}
